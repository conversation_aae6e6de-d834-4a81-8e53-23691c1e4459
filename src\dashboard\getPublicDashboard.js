const AWS = require('aws-sdk');

exports.handler = async (event) => {
  try {
    const quicksight = new AWS.QuickSight({ region: process.env.AWS_REGION });
    
    const params = {
      AwsAccountId: process.env.AWS_ACCOUNT_ID,
      DashboardId: process.env.QUICKSIGHT_DASHBOARD_ID,
      IdentityType: 'ANONYMOUS',
      SessionLifetimeInMinutes: 60,
      UndoRedoDisabled: true,
      ResetDisabled: true
    };

    const result = await quicksight.getDashboardEmbedUrl(params).promise();
    
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        embedUrl: result.EmbedUrl
      })
    };
    
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Dashboard unavailable' })
    };
  }
};
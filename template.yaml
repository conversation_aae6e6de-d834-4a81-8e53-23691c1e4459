AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Incident Reporting App - Auth & Authorization Infrastructure

Parameters:
  ProjectTag: { Type: String, Default: "incident-app" }
  GitRepoUrl:
    { Type: String, Default: https://github.com/drex7/cmrp-frontend.git }
  GitHubTokenSecret: { Type: String, Default: cmrp-frontend/token }
  Env: { Type: String, Default: dev }

Globals:
  Function:
    Timeout: 10
    MemorySize: 256
  Api:
    Auth:
      DefaultAuthorizer: CognitoAuthorizer
      Authorizers:
        CognitoAuthorizer:
          UserPoolArn: !GetAtt UserPool.Arn
    Cors:
      AllowMethods: "'GET, POST, OPTIONS, PUT'"
      AllowHeaders: "'Content-Type,Authorization'"
      AllowOrigin: "'http://localhost:4200'"

Resources:
  
  ################################################## Amplify ####################################################
  AmplifyRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: AmplifyServiceRole
      Description: IAM role assumed by AWS Amplify to build and deploy apps
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - amplify.amazonaws.com
                - amplify.eu-central-1.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AdministratorAccess-Amplify

  AmplifyApp:
    Type: AWS::Amplify::App
    Properties:
      Name: IncidentReportingAmplifyApp
      Description: Incident Reporting App
      Repository: !Ref GitRepoUrl
      IAMServiceRole: !GetAtt AmplifyRole.Arn
      AccessToken: !Sub "{{resolve:secretsmanager:${GitHubTokenSecret}:SecretString:GithubAccessToken_}}"
      BuildSpec: |
        version: 1 
        frontend:
          phases:
            preBuild:
              commands:
                - yarn install --cache-folder .yarn-cache --prefer-offline
                - npx ng version
            build:
              commands:
                  # generate src/environments/environment.ts
                - |
                  mkdir -p src/environments
                  echo "export const environment = {" > src/environments/environment.ts
                  echo "export const environment = {" > src/environments/environment.ts
                  echo "  authUrl: '$authUrl'," >> src/environments/environment.ts
                  echo "  identityPoolId: '$identityPoolId'," >> src/environments/environment.ts
                  echo "  userPoolId: '$userPoolId'," >> src/environments/environment.ts
                  echo "  userClientPoolId: '$userClientPoolId'," >> src/environments/environment.ts
                  echo "  incidentsUrl: '$incidentsUrl'," >> src/environments/environment.ts
                  echo "};" >> src/environments/environment.ts
                - yarn run build -- --configuration=production
          artifacts:
            baseDirectory: dist/cmrp-frontend/browser
            files:
              - '**/*'
          cache:
            paths:
              - .npm/**/*
              - node_modules/**/*
        redirects:
          - source: </^[^.]+$|\\.(?!(css|gif|ico|jpg|jpeg|js|png|svg|woff|woff2|ttf|map)$)([^.]+$)/>
            target: /index.html
            status: 200

  MainBranch:
    Type: AWS::Amplify::Branch
    Properties:
      AppId: !GetAtt AmplifyApp.AppId
      BranchName: main
      EnableAutoBuild: true
      Stage: PRODUCTION

  DevBranch:
    Type: AWS::Amplify::Branch
    Properties:
      AppId: !GetAtt AmplifyApp.AppId
      BranchName: dev
      EnableAutoBuild: true
      Stage: DEVELOPMENT


    # DynamoDB Table for storing incident reports
  
  
  ################################################## Amplify ####################################################
  IncidentTable:
    Type: AWS::DynamoDB::Table
    Properties:
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
        RecoveryPeriodInDays: 35

      AttributeDefinitions:
        - AttributeName: incidentId
          AttributeType: S
        - AttributeName: category
          AttributeType: S
        - AttributeName: status
          AttributeType: S
        - AttributeName: reportedBy
          AttributeType: S
        - AttributeName: region
          AttributeType: S
        - AttributeName: city
          AttributeType: S
        - AttributeName: assignedTo
          AttributeType: S
        - AttributeName: severity
          AttributeType: S
      KeySchema:
        - AttributeName: incidentId
          KeyType: HASH

      GlobalSecondaryIndexes:
        - IndexName: ByStatusIndex
          KeySchema:
            - AttributeName: status
              KeyType: HASH
            - AttributeName: incidentId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: BySeverityIndex
          KeySchema:
            - AttributeName: severity
              KeyType: HASH
            - AttributeName: incidentId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: ByCategoryIndex
          KeySchema:
            - AttributeName: category
              KeyType: HASH
            - AttributeName: incidentId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: ByReportedByIndex
          KeySchema:
            - AttributeName: reportedBy
              KeyType: HASH
            - AttributeName: incidentId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: ByAssignedToIndex
          KeySchema:
            - AttributeName: assignedTo
              KeyType: HASH
            - AttributeName: incidentId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: ByRegionIndex
          KeySchema:
            - AttributeName: region
              KeyType: HASH
            - AttributeName: incidentId
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        - IndexName: ByCityCategoryIndex
          KeySchema:
            - AttributeName: city
              KeyType: HASH
            - AttributeName: category
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: ProjectTag
          Value: !Ref ProjectTag

  UserPoolBackupTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: UserPoolBackupTable
      BillingMode: PAY_PER_REQUEST
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true

      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: backupDate
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: backupDate
          KeyType: RANGE

      Tags:
        - Key: ProjectTag
          Value: !Ref ProjectTag
  
  
  ################################################## Cognito ####################################################

  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: IncidentReportingUserPool
      UsernameAttributes:
        - email
      AutoVerifiedAttributes:
        - email
      LambdaConfig:
        CustomMessage: !GetAtt CustomMessageFunction.Arn
      Schema:
        - Name: name
          AttributeDataType: String
          Mutable: true
          Required: true
        - Name: region
          AttributeDataType: String
          Mutable: true
          Required: false
        - Name: country
          AttributeDataType: String
          Mutable: true
          Required: false
        - Name: city
          AttributeDataType: String
          Mutable: true
          Required: false
        - Name: telephone
          AttributeDataType: String
          Mutable: true
          Required: false
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireLowercase: true
          RequireUppercase: true
          RequireNumbers: true
          RequireSymbols: false

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: IncidentReportingFrontendClient
      UserPoolId: !Ref UserPool
      GenerateSecret: false
      ExplicitAuthFlows:
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
        - ALLOW_USER_SRP_AUTH
      SupportedIdentityProviders:
        - COGNITO
      PreventUserExistenceErrors: ENABLED
  
  IdentityPool:
    Type: AWS::Cognito::IdentityPool
    Properties:
      IdentityPoolName: IncidentReportingIdentityPool
      AllowUnauthenticatedIdentities: false
      CognitoIdentityProviders:
        - ClientId: !Ref UserPoolClient
          ProviderName: !GetAtt UserPool.ProviderName

  # Cognito Groups
  AdminGroup:
    Type: AWS::Cognito::UserPoolGroup
    Properties:
      GroupName: Admin
      UserPoolId: !Ref UserPool
      Precedence: 1

  CityOfficialGroup:
    Type: AWS::Cognito::UserPoolGroup
    Properties:
      GroupName: CityOfficial
      UserPoolId: !Ref UserPool
      Precedence: 2

  # IdentityPool Roles
  IdentityPoolAuthRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: IncidentReportingAuthRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Federated: cognito-identity.amazonaws.com
            Action: sts:AssumeRoleWithWebIdentity
            Condition:
              StringEquals:
                cognito-identity.amazonaws.com:aud: !Ref IdentityPool
              "ForAnyValue:StringLike":
                cognito-identity.amazonaws.com:amr: authenticated
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonCognitoReadOnly
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  IdentityPoolUnauthRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: IncidentReportingUnauthRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Deny
            Principal:
              Federated: cognito-identity.amazonaws.com
            Action: sts:AssumeRoleWithWebIdentity
            Condition:
              StringEquals:
                cognito-identity.amazonaws.com:aud: !Ref IdentityPool
              "ForAnyValue:StringLike":
                cognito-identity.amazonaws.com:amr: unauthenticated
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonCognitoReadOnly

  # Lambda Functions
  GetUploadUrlsFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/incidents/
      Handler: getUploadUrls.handler
      Runtime: nodejs22.x
      Policies:
        - S3WritePolicy:
            BucketName: !Ref IncidentAttachmentBucket
      Events:
        Api:
          Type: Api
          Properties:
            # RestApiId: !Ref ApiGateway
            Path: /upload-urls
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        OptionsEvent:
          Type: Api
          Properties:
            Path: /upload-urls
            Method: OPTIONS
            Auth:
              Authorizer: NONE

  CreateIncidentFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/incidents/
      Handler: createIncident.handler
      Runtime: nodejs22.x
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref IncidentTable
        - SNSPublishMessagePolicy:
            TopicName: !Ref IncidentReportedTopic
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:ListUsers
              Resource: !GetAtt UserPool.Arn
      Events:
        Api:
          Type: Api
          Properties:
            # RestApiId: !Ref ApiGateway
            Path: /incidents
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
  
  IdentityPoolRoleAttachment:
    Type: AWS::Cognito::IdentityPoolRoleAttachment
    Properties:
      IdentityPoolId: !Ref IdentityPool
      Roles:
        authenticated: !GetAtt IdentityPoolAuthRole.Arn
        unauthenticated: !GetAtt IdentityPoolUnauthRole.Arn

  AuthUtilsLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: IncidentReportingAuthUtils
      Description: Shared authorization utilities for admin-only Lambdas
      ContentUri: src/layers/auth_utils/
      CompatibleRuntimes:
        - python3.13

  CustomMessagePermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref CustomMessageFunction
      Principal: cognito-idp.amazonaws.com
      SourceArn: !GetAtt UserPool.Arn
  
  
  ################################################## APIs & Functions ##########################################

  # # API Gateway
  # ApiGateway:
  #   Type: AWS::Serverless::Api
  #   Properties:
  #     StageName: prod
  #     BinaryMediaTypes:
  #       - "multipart/form-data"
  #       - "image/*"
  #       - "application/octet-stream"
  #     # Cors:
  #     #   AllowMethods: "'OPTIONS,GET,POST,PUT'"
  #     #   AllowHeaders: "'Content-Type,Authorization,X-Amz-Date,X-Api-Key'"
  #     #   AllowOrigin: "'*'"
  #     #   AllowCredentials: false
  #     Auth:
  #       DefaultAuthorizer: CognitoAuthorizer
  #       Authorizers:
  #         CognitoAuthorizer:
  #           UserPoolArn: !GetAtt UserPool.Arn

  AdminInviteFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: AdminInviteUserFunction
      Description: Allows Admins to invite new Admins or City Officials
      Runtime: python3.13
      Handler: app.lambda_handler
      CodeUri: src/admin_invite/
      Environment:
        Variables:
          USER_POOL_ID: !Ref UserPool
      Layers:
        - !Ref AuthUtilsLayer
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - cognito-idp:AdminCreateUser
                - cognito-idp:AdminAddUserToGroup
              Resource: !GetAtt UserPool.Arn
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /invite
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        OptionsEvent:
          Type: Api
          Properties:
            Path: /invite
            Method: OPTIONS
            Auth:
              Authorizer: NONE

  CustomMessageFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: CognitoCustomMessageFunction
      Description: Customizes Cognito emails for invites and password resets
      Runtime: python3.13
      Handler: app.lambda_handler
      CodeUri: src/custom_message/
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - logs:CreateLogGroup
                - logs:CreateLogStream
                - logs:PutLogEvents
              Resource: "*"

  ListUsersFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: ListUserStatsFunction
      Description: Lists user counts by group and returns all users
      Runtime: python3.13
      Handler: app.lambda_handler
      CodeUri: src/list_users/
      Environment:
        Variables:
          USER_POOL_ID: !Ref UserPool
      Layers:
        - !Ref AuthUtilsLayer
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - cognito-idp:ListUsers
                - cognito-idp:ListUsersInGroup
                - cognito-idp:AdminListGroupsForUser
              Resource: !GetAtt UserPool.Arn
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /users
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        OptionsEvent:
          Type: Api
          Properties:
            Path: /users
            Method: OPTIONS
            Auth:
              Authorizer: NONE

  GetMyIncidentsFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/incidents/
      Handler: getMyIncidents.handler
      Runtime: nodejs22.x
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref IncidentTable
      Events:
        Api:
          Type: Api
          Properties:
            # RestApiId: !Ref ApiGateway
            Path: /incidents/mine
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        OptionsEvent:
          Type: Api
          Properties:
            Path: /incidents/mine
            Method: OPTIONS
            Auth:
              Authorizer: NONE
  
  ListIncidentsFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/incidents/
      Handler: listIncidents.handler
      Runtime: nodejs22.x
      Environment:
        Variables:
          INCIDENT_TABLE: !Ref IncidentTable
          ENV: !Ref Env
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref IncidentTable
      Events:
        Api:
          Type: Api
          Properties:
            # RestApiId: !Ref ApiGateway
            Path: /incidents
            Method: GET
            Auth:
              Authorizer: NONE
        OptionsEvent:
          Type: Api
          Properties:
            Path: /incidents
            Method: OPTIONS
            Auth:
              Authorizer: NONE

  UpdateIncidentStatusFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/incidents/
      Handler: updateIncidentStatus.handler
      Runtime: nodejs22.x
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref IncidentTable
        - SNSPublishMessagePolicy:
            TopicName: !Ref StatusUpdatedTopic
      Events:
        Api:
          Type: Api
          Properties:
            # RestApiId: !Ref ApiGateway
            Path: /incidents/{id}/status
            Method: PUT
            Auth:
              Authorizer: NONE
  
  SubmitFeedbackFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/feedback/
      Handler: submitFeedback.handler
      Runtime: nodejs22.x
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref IncidentTable
      Events:
        Api:
          Type: Api
          Properties:
            # RestApiId: !Ref ApiGateway
            Path: /feedback
            Method: POST
            Auth:
              Authorizer: NONE
        OptionsEvent:
          Type: Api
          Properties:
            Path: /feedback
            Method: OPTIONS
            Auth:
              Authorizer: NONE
  
  NotifyOfficialFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/notifications/
      Handler: notifyOfficial.handler
      Runtime: nodejs22.x
      Environment:
        Variables:
          INCIDENT_TABLE: !Ref IncidentTable
          INCIDENT_REPORTED_TOPIC: !Ref IncidentReportedTopic
      Policies:
        - SNSPublishMessagePolicy:
            TopicName: !Ref IncidentReportedTopic
        - DynamoDBReadPolicy:
            TableName: !Ref IncidentTable
      Events:
        IncidentReportedEvent:
          Type: SNS
          Properties:
            Topic: !Ref IncidentReportedTopic
  
  NotifyReporterFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/notifications/
      Handler: notifyReporter.handler
      Runtime: nodejs22.x
      Policies:
        - SNSPublishMessagePolicy:
            TopicName: !Ref StatusUpdatedTopic
      Events:
        StatusUpdatedEvent:
          Type: SNS
          Properties:
            Topic: !Ref StatusUpdatedTopic

  GetPublicDashboardFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/dashboard/
      Handler: getPublicDashboard.handler
      Runtime: nodejs22.x
      Environment:
        Variables:
          QUICKSIGHT_DASHBOARD_ID: "9824a03f-fef9-4507-b432-bdd619b87d96"
          AWS_ACCOUNT_ID: "GroupOne"
          AWS_REGION: "eu-west-1"
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - quicksight:GetDashboardEmbedUrl
                - quicksight:GenerateEmbedUrlForAnonymousUser
              Resource:
                - !Sub "arn:aws:quicksight:eu-west-1:GroupOne:dashboard/9824a03f-fef9-4507-b432-bdd619b87d96"
      Events:
        Api:
          Type: Api
          Properties:
            Path: /dashboard
            Method: GET
            Auth:
              Authorizer: NONE
        OptionsEvent:
          Type: Api
          Properties:
            Path: /dashboard
            Method: OPTIONS
            Auth:
              Authorizer: NONE

  IncidentAttachmentBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "incident-attachments-${AWS::AccountId}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true

  # SNS Topics
  IncidentReportedTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: "incident-reported"
  StatusUpdatedTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: "status-updated"


Outputs:
  
  IncidentTableName:
    Description: The name of the DynamoDB table for incident reports
    Value: !Ref IncidentTable
  UserPoolBackupTableName:
    Description: The name of the DynamoDB table for incident reports
    Value: !Ref UserPoolBackupTable

  AmplifyAppId:
    Description: The ID of the deployed Amplify application
    Value: !GetAtt AmplifyApp.AppId
  AmplifyAppName:
    Description: The name of the deployed Amplify application
    Value: !GetAtt AmplifyApp.AppName

  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref UserPool

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref UserPoolClient

  IdentityPoolId:
    Description: Cognito Identity Pool ID
    Value: !Ref IdentityPool

  ApiGatewayUrl:
    Description: Base URL for API Gateway
    Value: !Sub "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/dev"
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ba0cc1a6-fe7f-4ea4-882f-18d5aa610ad7" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/incidents/createIncident.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/incidents/createIncident.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/incidents/updateIncidentStatus.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/incidents/updateIncidentStatus.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/notifications/notifyOfficial.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/notifications/notifyOfficial.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/notifications/notifyReporter.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/notifications/notifyReporter.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/template.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/template.yaml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="31Hip6CS0z5FXxaGRz0Td1436OS" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;incidents&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;javascript.nodejs.core.library.configured.version&quot;: &quot;22.14.0&quot;,
    &quot;javascript.nodejs.core.library.typings.version&quot;: &quot;22.14.1&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\WebStorm 2024.3.5\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\Grp-Incidence-mgt-aws" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-WS-243.26053.12" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ba0cc1a6-fe7f-4ea4-882f-18d5aa610ad7" name="Changes" comment="" />
      <created>1755187578407</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755187578407</updated>
      <workItem from="1755187580016" duration="614000" />
      <workItem from="1755256835363" duration="3979000" />
      <workItem from="1755264288787" duration="3267000" />
      <workItem from="1755529250718" duration="7818000" />
      <workItem from="1755605290004" duration="2370000" />
      <workItem from="1755677083557" duration="5788000" />
      <workItem from="1755862405337" duration="1333000" />
      <workItem from="1756460949690" duration="170000" />
      <workItem from="1756461169515" duration="5699000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>
AWSTemplateFormatVersion: "2010-09-09"
Description: AWS Cognito Setup for Monitoring & Reporting Platform

Resources:
  # Cognito User Pool
  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: MonitoringUserPool
      AutoVerifiedAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireLowercase: true
          RequireUppercase: true
          RequireNumbers: true
          RequireSymbols: true
      Schema:
        - Name: email
          AttributeDataType: String
          Required: true
          Mutable: true
        - Name: name
          AttributeDataType: String
          Required: true
          Mutable: true
      UsernameAttributes:
        - email

  # Cognito App Client
  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: MonitoringUserPoolClient
      UserPoolId: !Ref UserPool
      GenerateSecret: false
      ExplicitAuthFlows:
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH

  # Cognito User Groups
  CitizenGroup:
    Type: AWS::Cognito::UserPoolGroup
    Properties:
      GroupName: citizen
      UserPoolId: !Ref UserPool
      Description: Citizen group for reporting incidents
      Precedence: 0

  CityAuthGroup:
    Type: AWS::Cognito::UserPoolGroup
    Properties:
      GroupName: cityAuth
      UserPoolId: !Ref UserPool
      Description: City authority group for managing incidents
      Precedence: 1

  AdminGroup:
    Type: AWS::Cognito::UserPoolGroup
    Properties:
      GroupName: admin
      UserPoolId: !Ref UserPool
      Description: Admin group for full access
      Precedence: 2

Outputs:
  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref UserPool
  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref UserPoolClient
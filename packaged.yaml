AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Monitoring & Reporting Platform Backend (MSc Real Estate Capstone)
Globals:
  Function:
    Runtime: nodejs18.x
    Timeout: 10
    MemorySize: 256
    CodeUri: ./src
    Environment:
      Variables:
        INCIDENT_TABLE:
          Ref: IncidentTable
        FEEDBACK_TABLE:
          Ref: FeedbackTable
        ATTACHMENT_BUCKET:
          Ref: IncidentAttachmentBucket
        INCIDENT_REPORTED_TOPIC:
          Ref: IncidentReportedTopic
        STATUS_UPDATED_TOPIC:
          Ref: StatusUpdatedTopic
Resources:
  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: MonitoringUserPool
      AutoVerifiedAttributes:
      - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
      Schema:
      - Name: email
        Required: true
        Mutable: true
      UsernameAttributes:
      - email
    Metadata:
      SamResourceId: UserPool
  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: MonitoringUserPoolClient
      UserPoolId:
        Ref: UserPool
      GenerateSecret: false
    Metadata:
      SamResourceId: UserPoolClient
  UserPoolGroups:
    Type: AWS::CloudFormation::Stack
    Properties:
      TemplateURL: https://sam-artifacts-dev-kesse.s3.eu-west-1.amazonaws.com/cognito-user-pool-groups.yaml
      Parameters:
        UserPoolId:
          Ref: UserPool
        Groups: citizen,cityAuth,admin
    Metadata:
      SamResourceId: UserPoolGroups
  IncidentTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: IncidentTable
      AttributeDefinitions:
      - AttributeName: id
        AttributeType: S
      KeySchema:
      - AttributeName: id
        KeyType: HASH
      BillingMode: PAY_PER_REQUEST
    Metadata:
      SamResourceId: IncidentTable
  FeedbackTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: FeedbackTable
      AttributeDefinitions:
      - AttributeName: id
        AttributeType: S
      KeySchema:
      - AttributeName: id
        KeyType: HASH
      BillingMode: PAY_PER_REQUEST
    Metadata:
      SamResourceId: FeedbackTable
  IncidentAttachmentBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName:
        Fn::Sub: incident-attachments-${AWS::AccountId}
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
    Metadata:
      SamResourceId: IncidentAttachmentBucket
  IncidentReportedTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: incident-reported
    Metadata:
      SamResourceId: IncidentReportedTopic
  StatusUpdatedTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: status-updated
    Metadata:
      SamResourceId: StatusUpdatedTopic
  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: prod
      BinaryMediaTypes:
      - multipart/form-data
      - image/*
      - application/octet-stream
      Cors:
        AllowMethods: '''OPTIONS,GET,POST,PUT'''
        AllowHeaders: '''Content-Type,Authorization,X-Amz-Date,X-Api-Key'''
        AllowOrigin: '''*'''
        AllowCredentials: false
      Auth:
        DefaultAuthorizer: CognitoAuthorizer
        Authorizers:
          CognitoAuthorizer:
            UserPoolArn:
              Fn::GetAtt:
              - UserPool
              - Arn
    Metadata:
      SamResourceId: ApiGateway
  GetUploadUrlsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: incidents/getUploadUrls.handler
      Policies:
      - S3WritePolicy:
          BucketName:
            Ref: IncidentAttachmentBucket
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId:
              Ref: ApiGateway
            Path: /upload-urls
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer
      CodeUri: s3://sam-artifacts-dev-kesse/d5f48b255e33756fb652e8afde6c7ca0
    Metadata:
      SamResourceId: GetUploadUrlsFunction
  CreateIncidentFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: incidents/createIncident.handler
      Policies:
      - DynamoDBCrudPolicy:
          TableName:
            Ref: IncidentTable
      - SNSPublishMessagePolicy:
          TopicName:
            Ref: IncidentReportedTopic
      - Statement:
        - Effect: Allow
          Action:
          - cognito-idp:ListUsers
          Resource:
            Fn::GetAtt:
            - UserPool
            - Arn
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId:
              Ref: ApiGateway
            Path: /incidents
            Method: post
            Auth:
              Authorizer: CognitoAuthorizer
      CodeUri: s3://sam-artifacts-dev-kesse/d5f48b255e33756fb652e8afde6c7ca0
    Metadata:
      SamResourceId: CreateIncidentFunction
  GetMyIncidentsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: incidents/getMyIncidents.handler
      Policies:
      - DynamoDBReadPolicy:
          TableName:
            Ref: IncidentTable
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId:
              Ref: ApiGateway
            Path: /incidents/mine
            Method: get
            Auth:
              Authorizer: CognitoAuthorizer
      CodeUri: s3://sam-artifacts-dev-kesse/d5f48b255e33756fb652e8afde6c7ca0
    Metadata:
      SamResourceId: GetMyIncidentsFunction
  ListIncidentsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: incidents/listIncidents.handler
      Policies:
      - DynamoDBReadPolicy:
          TableName:
            Ref: IncidentTable
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId:
              Ref: ApiGateway
            Path: /incidents
            Method: get
            Auth:
              Authorizer: NONE
      CodeUri: s3://sam-artifacts-dev-kesse/d5f48b255e33756fb652e8afde6c7ca0
    Metadata:
      SamResourceId: ListIncidentsFunction
  UpdateIncidentStatusFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: incidents/updateIncidentStatus.handler
      Policies:
      - DynamoDBCrudPolicy:
          TableName:
            Ref: IncidentTable
      - SNSPublishMessagePolicy:
          TopicName:
            Ref: StatusUpdatedTopic
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId:
              Ref: ApiGateway
            Path: /incidents/{id}/status
            Method: put
            Auth:
              Authorizer: NONE
      CodeUri: s3://sam-artifacts-dev-kesse/d5f48b255e33756fb652e8afde6c7ca0
    Metadata:
      SamResourceId: UpdateIncidentStatusFunction
  SubmitFeedbackFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: feedback/submitFeedback.handler
      Policies:
      - DynamoDBCrudPolicy:
          TableName:
            Ref: FeedbackTable
      Events:
        Api:
          Type: Api
          Properties:
            RestApiId:
              Ref: ApiGateway
            Path: /feedback
            Method: post
            Auth:
              Authorizer: NONE
      CodeUri: s3://sam-artifacts-dev-kesse/d5f48b255e33756fb652e8afde6c7ca0
    Metadata:
      SamResourceId: SubmitFeedbackFunction
  NotifyOfficialFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: notifications/notifyOfficial.handler
      Policies:
      - SNSPublishMessagePolicy:
          TopicName:
            Ref: IncidentReportedTopic
      CodeUri: s3://sam-artifacts-dev-kesse/d5f48b255e33756fb652e8afde6c7ca0
    Metadata:
      SamResourceId: NotifyOfficialFunction
  NotifyReporterFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: notifications/notifyReporter.handler
      Policies:
      - SNSPublishMessagePolicy:
          TopicName:
            Ref: StatusUpdatedTopic
      CodeUri: s3://sam-artifacts-dev-kesse/d5f48b255e33756fb652e8afde6c7ca0
    Metadata:
      SamResourceId: NotifyReporterFunction
Outputs:
  ApiUrl:
    Description: API Gateway endpoint URL
    Value:
      Fn::Sub: https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/prod/
  UserPoolId:
    Description: Cognito User Pool ID
    Value:
      Ref: UserPool
  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value:
      Ref: UserPoolClient
  IncidentTableName:
    Description: Incident DynamoDB Table Name
    Value:
      Ref: IncidentTable
  FeedbackTableName:
    Description: Feedback DynamoDB Table Name
    Value:
      Ref: FeedbackTable
  AttachmentBucketName:
    Description: Incident Attachment S3 Bucket Name
    Value:
      Ref: IncidentAttachmentBucket
  IncidentReportedTopicArn:
    Description: Incident Reported SNS Topic ARN
    Value:
      Ref: IncidentReportedTopic
  StatusUpdatedTopicArn:
    Description: Status Updated SNS Topic ARN
    Value:
      Ref: StatusUpdatedTopic